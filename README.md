# Momentum - Task Management App

A modern, decentralized task management application built with React, TypeScript, and Tailwind CSS.

## Features

- **Decentralized Identity Management**: Secure, client-side identity creation and management
- **Project Management**: Create and organize multiple projects
- **Task Management**: Full CRUD operations for tasks with drag-and-drop support
- **Multiple Views**: Dashboard, Board (Kanban), List, Calendar, Gantt, and Day Planner views
- **Real-time Updates**: Local storage with user-specific data isolation
- **Responsive Design**: Modern UI with Tailwind CSS

## Tech Stack

- **Frontend**: React 19 + TypeScript
- **Styling**: Tailwind CSS
- **Drag & Drop**: @dnd-kit
- **Icons**: Lucide React
- **Build Tool**: Vite
- **Testing**: Vitest + Testing Library

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open [http://localhost:5173](http://localhost:5173) in your browser

### Authentication

1. First, visit the login page at `client/login.html` to create an identity
2. Generate a secure identity with a password
3. Use the identity to access the main application

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview preview build
- `npm run test` - Run tests
- `npm run lint` - Run ESLint

## Project Structure

```
src/
├── App.tsx          # Main application component
├── index.css        # Global styles with Tailwind
├── main.tsx         # Application entry point
└── test-setup.ts    # Test configuration

client/
└── login.html       # Identity management page
```

## Security Features

- Client-side encryption using Web Crypto API
- Secure key generation with entropy collection
- Password-protected identity storage
- Session-based authentication

## Development

The application uses modern React patterns with TypeScript for type safety. All data is stored locally using localStorage with user-specific namespacing for security.

## License

MIT License