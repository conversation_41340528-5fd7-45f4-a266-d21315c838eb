import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { DndContext, closestCenter, PointerSensor, useSensor, useSensors, DragOverlay } from '@dnd-kit/core';
import { SortableContext, useSortable, arrayMove, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Plus, User, Trash2, CheckCircle, Calendar as CalendarIcon, LayoutGrid, List, ChevronLeft, ChevronRight, Sparkles, Clock, LayoutDashboard, Folder, ChevronsLeft, ChevronsRight, Star, Sun, Diamond, MessageSquare, Repeat, LogOut } from 'lucide-react';

// --- Local Storage Utilities ---
const getFromStorage = (key, defaultValue = []) => {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.error(`Error reading from localStorage key “${key}”:`, error);
        return defaultValue;
    }
};

const saveToStorage = (key, value) => {
    try {
        localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
        console.error(`Error saving to localStorage key “${key}”:`, error);
    }
};


// --- Main App Component ---
export default function App() {
    const [user, setUser] = useState(null);
    const [isAuthReady, setIsAuthReady] = useState(false);
    const [projects, setProjects] = useState([]);
    const [activeProject, setActiveProject] = useState(null);
    const [tasks, setTasks] = useState([]);
    const [allTasks, setAllTasks] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [showTaskModal, setShowTaskModal] = useState(false);
    const [showProjectModal, setShowProjectModal] = useState(false);
    const [selectedTask, setSelectedTask] = useState(null);
    const [currentView, setCurrentView] = useState('Dashboard');
    const [isDrawerOpen, setIsDrawerOpen] = useState(true);
    const [plannerDate, setPlannerDate] = useState(new Date());
    
    const statuses = useMemo(() => ['Backlog', 'In Progress', 'Review', 'Done'], []);
    
    // --- Secure Identity Check ---
    useEffect(() => {
        const session = sessionStorage.getItem('momentum_session');
        if (!session) {
            setIsAuthReady(true); // Stop loading, but user will be null
        } else {
            const parsedSession = JSON.parse(session);
            setUser({
                name: parsedSession.name,
                publicKey: parsedSession.publicKey,
                uid: parsedSession.publicKey // Use public key as the unique ID
            });
            setIsAuthReady(true);
        }
    }, []);

    // --- Data Loading from localStorage (User-Specific) ---
    useEffect(() => {
        if (!isAuthReady || !user) {
            setIsLoading(false);
            return;
        };
        setIsLoading(true);
        const projectsKey = `momentum_projects_${user.publicKey}`;
        const tasksKey = `momentum_tasks_${user.publicKey}`;
        
        const storedProjects = getFromStorage(projectsKey, []);
        const storedTasks = getFromStorage(tasksKey, []);
        
        setProjects(storedProjects);
        setAllTasks(storedTasks);

        if (!activeProject && storedProjects.length > 0) {
            setActiveProject(storedProjects[0]);
        } else if (storedProjects.length === 0) {
            setActiveProject(null);
        }
        setIsLoading(false);
    }, [isAuthReady, user]);

    // --- Filter tasks when active project changes ---
    useEffect(() => {
        if (activeProject) {
            const projectTasks = allTasks.filter(t => t.projectId === activeProject.id);
            setTasks(projectTasks);
        } else {
            setTasks([]);
        }
    }, [activeProject, allTasks]);
    
    // --- Memoized Data ---
    const allMilestones = useMemo(() => allTasks.filter(t => t.isMilestone).map(t => ({...t, projectName: projects.find(p => p.id === t.projectId)?.name })), [allTasks, projects]);
    const dailyTasks = useMemo(() => {
        if (currentView !== 'Day' || projects.length === 0) return [];
        const startOfDay = new Date(plannerDate); startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(plannerDate); endOfDay.setHours(23, 59, 59, 999);
        return allTasks.filter(t => t.dueDate && new Date(t.dueDate) >= startOfDay && new Date(t.dueDate) <= endOfDay).map(t => ({...t, projectName: projects.find(p => p.id === t.projectId)?.name })).sort((a,b) => new Date(a.dueDate) - new Date(b.dueDate));
    }, [currentView, plannerDate, allTasks, projects]);

    const tasksByStatus = useMemo(() => { const grouped = {}; statuses.forEach(status => { grouped[status] = tasks.filter(task => task.status === status).sort((a,b) => a.order - b.order); }); return grouped; }, [tasks, statuses]);
    const tasksWithDates = useMemo(() => tasks.filter(task => !!task.startDate && !!task.dueDate), [tasks]);
    
    // --- Handlers ---
    const handleSaveProject = (projectName) => {
        const newProject = { id: crypto.randomUUID(), name: projectName, createdAt: new Date().toISOString() };
        const updatedProjects = [...projects, newProject];
        setProjects(updatedProjects);
        saveToStorage(`momentum_projects_${user.publicKey}`, updatedProjects);
        setActiveProject(newProject);
        setCurrentView('Board');
        setShowProjectModal(false);
    };
    
    const handleProjectTasksUpdate = (updatedTasksForProject) => {
        const otherTasks = allTasks.filter(t => t.projectId !== activeProject.id);
        const newAllTasks = [...otherTasks, ...updatedTasksForProject];
        setAllTasks(newAllTasks);
        saveToStorage(`momentum_tasks_${user.publicKey}`, newAllTasks);
    };

    const handleSaveTask = (taskData) => {
        const projectId = taskData.projectId || activeProject?.id;
        if (!projectId) { alert("Could not find a project for this task."); return; }
        
        let updatedTasks;
        const timestamp = new Date().toISOString();
        
        if (taskData.id) { // Update
            const originalTask = allTasks.find(t => t.id === taskData.id);
            const updatedTask = { ...originalTask, ...taskData };
            const historyEntry = { id: crypto.randomUUID(), type: 'history', changedBy: user.publicKey, timestamp, changes: [] };
            const dataToCompare = { ...taskData };
            delete dataToCompare.id; delete dataToCompare.projectId;

            Object.keys(dataToCompare).forEach(key => {
                if(JSON.stringify(originalTask[key]) !== JSON.stringify(dataToCompare[key])){
                     historyEntry.changes.push({ field: key, oldValue: String(originalTask[key] || 'Not set'), newValue: String(dataToCompare[key] || 'Not set') });
                }
            });
            if(historyEntry.changes.length > 0) updatedTask.activity = [...(updatedTask.activity || []), historyEntry];
            updatedTasks = allTasks.map(t => t.id === taskData.id ? updatedTask : t);
        } else { // Create
            const newTask = {
                ...taskData,
                id: crypto.randomUUID(), projectId, order: tasks.filter(t => t.status === taskData.status).length, createdAt: timestamp,
                activity: [{ id: crypto.randomUUID(), type: 'history', changes: [{ field: 'Task', oldValue: '', newValue: 'Created' }], changedBy: user.publicKey, timestamp }]
            };
            updatedTasks = [...allTasks, newTask];
        }
        setAllTasks(updatedTasks);
        saveToStorage(`momentum_tasks_${user.publicKey}`, updatedTasks);
        setShowTaskModal(false); setSelectedTask(null);
    };

    const handleAddComment = (taskId, text) => {
        if(!text.trim()) return;
        const updatedTasks = allTasks.map(task => {
            if (task.id === taskId) {
                const newComment = { id: crypto.randomUUID(), type: 'comment', text, authorId: user.publicKey, authorName: user.name, timestamp: new Date().toISOString() };
                return { ...task, activity: [...(task.activity || []), newComment] };
            }
            return task;
        });
        setAllTasks(updatedTasks);
        saveToStorage(`momentum_tasks_${user.publicKey}`, updatedTasks);
    };
    
    const handleNewTaskForDate = (date) => {
        if (!activeProject) { alert("Please select a project first."); return; }
        const isoDate = date.toISOString().split('T')[0];
        setSelectedTask({ startDate: isoDate, dueDate: isoDate });
        setShowTaskModal(true);
    };

    const handleDeleteTask = (taskId) => {
        if (window.confirm("Are you sure?")) {
            const updatedTasks = allTasks.filter(t => t.id !== taskId);
            setAllTasks(updatedTasks);
            saveToStorage(`momentum_tasks_${user.publicKey}`, updatedTasks);
        }
    };

    const handleLogout = () => {
        sessionStorage.removeItem('momentum_session');
        setUser(null);
    };
    
    // --- Render Logic ---
    if (!isAuthReady || isLoading) return <LoadingSpinner fullScreen={true} />;
    if (!user) return <LoginRedirectScreen />;


    const renderView = () => {
        if (!activeProject && projects.length > 0 && !isLoading) { setActiveProject(projects[0]); return <LoadingSpinner />; }
        
        switch(currentView) {
            case 'Dashboard': return <DashboardView project={activeProject} tasks={tasks} allMilestones={allMilestones} />;
            case 'Day': return <DayPlannerView tasks={dailyTasks} date={plannerDate} setDate={setPlannerDate} onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }} />;
            case 'Board': if (!activeProject) return <NoProjectsView onNewProject={() => setShowProjectModal(true)} />; return <BoardView tasks={tasks} setTasks={setTasks} statuses={statuses} tasksByStatus={tasksByStatus} onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }} onDeleteTask={handleDeleteTask} onAddTask={() => handleNewTaskForDate(new Date())} activeProject={activeProject} user={user} />;
            case 'List': if (!activeProject) return <NoProjectsView onNewProject={() => setShowProjectModal(true)} />; return <ListView statuses={statuses} tasksByStatus={tasksByStatus} onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }} onDeleteTask={handleDeleteTask}/>;
            case 'Calendar': if (!activeProject) return <NoProjectsView onNewProject={() => setShowProjectModal(true)} />; return <CalendarView tasks={tasks} onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }} onNewTaskForDate={handleNewTaskForDate} />;
            case 'Gantt': if (!activeProject) return <NoProjectsView onNewProject={() => setShowProjectModal(true)} />; return <GanttView tasks={tasksWithDates} project={activeProject} onTasksUpdate={handleProjectTasksUpdate} onEditTask={(t) => { setSelectedTask(t); setShowTaskModal(true); }} user={user}/>;
            default: return <DashboardView project={activeProject} tasks={tasks} allMilestones={allMilestones} />;
        }
    };

    return (
        <div className="flex h-screen bg-gray-900 text-gray-100 font-sans">
            <AppDrawer isOpen={isDrawerOpen} projects={projects} activeProject={activeProject} setActiveProject={setActiveProject} onNewProject={() => setShowProjectModal(true)} currentView={currentView} setCurrentView={setCurrentView} onLogout={handleLogout} user={user} />
            <div className="flex-1 flex flex-col overflow-hidden">
                <Header onAddTask={() => handleNewTaskForDate(new Date())} user={user} isDrawerOpen={isDrawerOpen} toggleDrawer={() => setIsDrawerOpen(!isDrawerOpen)} activeProject={activeProject} currentView={currentView}/>
                <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">{renderView()}</main>
            </div>
            {showTaskModal && <TaskModal task={selectedTask} project={activeProject || projects.find(p=>p.id === selectedTask.projectId)} user={user} onClose={() => {setShowTaskModal(false); setSelectedTask(null);}} onSave={handleSaveTask} onDelete={handleDeleteTask} onAddComment={handleAddComment} statuses={statuses}/>}
            {showProjectModal && <ProjectModal onClose={() => setShowProjectModal(false)} onSave={handleSaveProject} />}
        </div>
    );
}

// --- App Structure Components ---
const AppDrawer = ({ isOpen, projects, activeProject, setActiveProject, onNewProject, currentView, setCurrentView, onLogout, user }) => ( <div className={`bg-gray-800 text-white flex flex-col transition-all duration-300 ${isOpen ? 'w-64' : 'w-0'} overflow-hidden`}> <div className="p-4 border-b border-gray-700 flex-shrink-0"><h2 className="text-xl font-bold">Momentum</h2></div> <nav className="flex-1 p-2 space-y-1 overflow-y-auto"> <button onClick={() => { setActiveProject(null); setCurrentView('Dashboard');}} className={`w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium ${currentView === 'Dashboard' ? 'bg-indigo-600' : 'hover:bg-gray-700'}`}><LayoutDashboard size={18} /> Dashboard</button> <button onClick={() => { setActiveProject(null); setCurrentView('Day');}} className={`w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium ${currentView === 'Day' ? 'bg-indigo-600' : 'hover:bg-gray-700'}`}><Sun size={18} /> Day Planner</button> <div className="pt-2 mt-2 border-t border-gray-700"><h3 className="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">Projects</h3></div> {projects.map(p => (<div key={p.id}><button onClick={() => { setActiveProject(p); if(['Dashboard', 'Day'].includes(currentView)) setCurrentView('Board'); }} className={`w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium ${activeProject?.id === p.id ? 'bg-indigo-600' : 'hover:bg-gray-700'}`}><Folder size={18} /> {p.name}</button>{activeProject?.id === p.id && (<div className="pl-8 mt-1 space-y-1"><button onClick={() => setCurrentView('Board')} className={`w-full text-left text-sm py-1 rounded-md px-2 ${currentView === 'Board' ? 'text-white' : 'text-gray-400 hover:text-white'}`}>Board</button><button onClick={() => setCurrentView('List')} className={`w-full text-left text-sm py-1 rounded-md px-2 ${currentView === 'List' ? 'text-white' : 'text-gray-400 hover:text-white'}`}>List</button><button onClick={() => setCurrentView('Calendar')} className={`w-full text-left text-sm py-1 rounded-md px-2 ${currentView === 'Calendar' ? 'text-white' : 'text-gray-400 hover:text-white'}`}>Calendar</button><button onClick={() => setCurrentView('Gantt')} className={`w-full text-left text-sm py-1 rounded-md px-2 ${currentView === 'Gantt' ? 'text-white' : 'text-gray-400 hover:text-white'}`}>Gantt</button></div>)}</div>))} </nav> <div className="p-2 border-t border-gray-700 flex-shrink-0"><button onClick={onNewProject} className="w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-700"><Plus size={18} /> New Project</button></div> <div className="p-2 border-t border-gray-700 flex-shrink-0"> <div className="flex items-center gap-2 p-2"> <User className="w-8 h-8 bg-gray-600 rounded-full p-1.5"/> <div><p className="font-semibold text-sm">{user?.name}</p><button onClick={onLogout} className="text-xs text-red-400 hover:underline">Logout</button></div> </div></div></div> );
const Header = ({ onAddTask, user, isDrawerOpen, toggleDrawer, activeProject, currentView }) => {
    const viewTitles = { 'Dashboard': 'Dashboard', 'Day': 'Day Planner', 'Board': activeProject?.name, 'List': activeProject?.name, 'Calendar': activeProject?.name, 'Gantt': activeProject?.name };
    return (<header className="flex items-center justify-between p-4 border-b border-gray-700/80 bg-gray-900/80 backdrop-blur-sm z-10 flex-shrink-0"> <div className="flex items-center space-x-4"><button onClick={toggleDrawer} className="p-2 rounded-md hover:bg-gray-700">{isDrawerOpen ? <ChevronsLeft size={20}/> : <ChevronsRight size={20} />}</button><h1 className="text-2xl font-bold text-white">{viewTitles[currentView] || 'Momentum'}</h1></div> <div className="flex items-center space-x-4">{activeProject && <button onClick={onAddTask} className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-500 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-opacity-75"><Plus size={18} /><span className="hidden md:inline">New Task</span></button>}</div> </header>);
};

// --- View Components ---
const LoginRedirectScreen = () => (
    <div className="w-screen h-screen flex flex-col items-center justify-center bg-gray-900 text-white">
        <h1 className="text-4xl font-bold mb-4">Access Denied</h1>
        <p className="text-lg text-gray-300">You must be logged in to use Momentum.</p>
        <p className="text-gray-400 mt-2">Please open the "Momentum: Identity & Login" page to continue.</p>
    </div>
);
const DashboardView = ({ project, tasks, allMilestones }) => (<div className="text-white"><h1 className="text-3xl font-bold mb-6">Master Dashboard</h1><div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"><div className="bg-gray-800 p-6 rounded-lg"><h3 className="text-gray-400 text-sm font-medium">Active Project</h3><p className="text-3xl font-semibold mt-2 truncate">{project?.name || 'N/A'}</p></div><div className="bg-gray-800 p-6 rounded-lg"><h3 className="text-gray-400 text-sm font-medium">Tasks in Project</h3><p className="text-3xl font-semibold mt-2">{tasks.length}</p></div><div className="bg-gray-800 p-6 rounded-lg"><h3 className="text-gray-400 text-sm font-medium">Total Milestones</h3><p className="text-3xl font-semibold mt-2">{allMilestones.length}</p></div><div className="bg-gray-800 p-6 rounded-lg"><h3 className="text-gray-400 text-sm font-medium">Completed in Project</h3><p className="text-3xl font-semibold mt-2">{tasks.filter(t => t.status === 'Done').length}</p></div></div><div className="mt-8"><h2 className="text-xl font-bold mb-4">All Project Milestones</h2><GanttView tasks={allMilestones} isMasterView={true} /></div></div>);
const DayPlannerView = ({ tasks, date, setDate, onEditTask }) => { const changeDay = (offset) => { setDate(prev => { const newDate = new Date(prev); newDate.setDate(newDate.getDate() + offset); return newDate; }); }; return (<div className="bg-gray-800/50 p-4 rounded-lg border border-gray-700/50"><header className="flex items-center justify-between p-4 border-b border-gray-700/50"><div className="flex items-center gap-4"><button onClick={() => changeDay(-1)} className="p-2 rounded-md hover:bg-gray-700"><ChevronLeft/></button><input type="date" value={date.toISOString().split('T')[0]} onChange={e => setDate(new Date(e.target.value))} className="bg-gray-700 rounded-md p-2 text-white" /><button onClick={() => changeDay(1)} className="p-2 rounded-md hover:bg-gray-700"><ChevronRight/></button></div><button onClick={() => setDate(new Date())} className="px-4 py-2 rounded-md border border-gray-600 hover:bg-gray-700 text-sm">Today</button></header><div className="p-4">{tasks.length > 0 ? (<div className="space-y-3">{tasks.map(task => (<div key={task.id} onClick={() => onEditTask(task)} className="bg-gray-800 p-3 rounded-lg border border-gray-700/50 hover:border-indigo-500/50 cursor-pointer flex items-center justify-between"><div><p className="text-base font-medium text-gray-200">{task.title}</p><p className="text-xs text-gray-400 mt-1">{task.projectName}</p></div><div className="flex items-center gap-4">{task.isMilestone && <Star size={16} className="text-amber-400" />}{task.dueDate && <span className="text-sm text-gray-300">{new Date(task.dueDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>}</div></div>))}</div>) : (<div className="text-center py-10 text-gray-400">No tasks scheduled for this day.</div>)}</div></div>); };
const NoProjectsView = ({ onNewProject }) => (<div className="flex flex-col items-center justify-center h-full text-center"><Folder size={64} className="text-gray-600 mb-4" /><h2 className="text-2xl font-bold text-white mb-2">No Projects Yet</h2><p className="text-gray-400 mb-6">Get started by creating your first project.</p><button onClick={onNewProject} className="flex items-center space-x-2 px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-500 transition-colors"><Plus size={20} /><span>Create Project</span></button></div>);
const BoardView = ({ tasks, setTasks, statuses, tasksByStatus, onEditTask, onAddTask, activeProject, user }) => {
    const sensors = useSensors( useSensor(PointerSensor, { activationConstraint: { distance: 8 } }) );
    const onDragEnd = async ({ active, over }) => { if (!over || active.id === over.id || !activeProject) return; const oldIndex = tasks.findIndex(t => t.id === active.id); const newIndex = tasks.findIndex(t => t.id === over.id); if(oldIndex !== -1 && newIndex !== -1) { const newTasks = arrayMove(tasks, oldIndex, newIndex); setTasks(newTasks); const allStoredTasks = getFromStorage(`momentum_tasks_${user.publicKey}`); const updatedTasks = allStoredTasks.map(t => newTasks.find(nt => nt.id === t.id) || t); saveToStorage(`momentum_tasks_${user.publicKey}`, updatedTasks); } };
    return (<DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={onDragEnd}><div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">{statuses.map(status => (<Column key={status} id={status} title={status} tasks={tasksByStatus[status] || []} onEditTask={onEditTask} onAddTask={onAddTask}/>))}</div></DndContext>);
};
const ListView = ({ statuses, tasksByStatus, onEditTask, onDeleteTask }) => (<div className="space-y-8">{statuses.map(status => { const tasksInStatus = tasksByStatus[status] || []; if (tasksInStatus.length === 0) return null; return (<div key={status}><div className="flex items-center space-x-3 mb-4"><h2 className="text-xl font-semibold text-white">{status}</h2><span className="text-sm text-gray-400 bg-gray-700/80 rounded-full px-2.5 py-1">{tasksInStatus.length}</span></div><div className="bg-gray-800/50 rounded-lg border border-gray-700/50"><div className="divide-y divide-gray-700/50">{tasksInStatus.map(task => (<ListViewRow key={task.id} task={task} onEditTask={onEditTask} onDeleteTask={onDeleteTask}/>))}</div></div></div>);})}</div>);
const CalendarView = ({ tasks, onEditTask, onNewTaskForDate }) => {
    const [currentDate, setCurrentDate] = useState(new Date());
    const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1); const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    const tasksByDate = useMemo(() => { const grouped = {}; (tasks || []).forEach(task => { if(!task.dueDate) return; const date = new Date(task.dueDate).toISOString().split('T')[0]; if (!grouped[date]) grouped[date] = []; grouped[date].push(task); }); return grouped; }, [tasks]);
    const changeMonth = (offset) => setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() + offset, 1));
    const calendarDays = Array.from({ length: firstDayOfMonth.getDay() }, (_, i) => <div key={`empty-${i}`} className="border-r border-b border-gray-700/50"></div>);
    for (let day = 1; day <= lastDayOfMonth.getDate(); day++) { const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day); const dateString = date.toISOString().split('T')[0]; const isToday = new Date().toISOString().split('T')[0] === dateString; const dayTasks = tasksByDate[dateString] || []; calendarDays.push(<div key={day} className="border-r border-b border-gray-700/50 p-2 min-h-[140px] flex flex-col hover:bg-gray-700/30 cursor-pointer" onClick={() => onNewTaskForDate(date)}><span className={`font-semibold ${isToday ? 'bg-indigo-600 rounded-full w-8 h-8 flex items-center justify-center text-white' : 'text-gray-300'}`}>{day}</span><div className="mt-2 space-y-1 overflow-y-auto">{dayTasks.map(task => (<div key={task.id} onClick={(e) => { e.stopPropagation(); onEditTask(task); }} className={`p-1.5 rounded-md text-sm text-gray-200 cursor-pointer truncate flex flex-col ${task.isMilestone ? 'bg-amber-600' : 'bg-gray-700 hover:bg-indigo-500'}`}><span>{task.title}</span>{task.deadline && !task.isMilestone && <span className="text-xs text-red-400/80 flex items-center gap-1 mt-1"><Clock size={12}/>Must be done</span>}</div>))}</div></div>);}
    const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return (<div className="bg-gray-800/50 rounded-lg border border-gray-700/50"><header className="flex items-center justify-between p-4 border-b border-gray-700/50"><div className="flex items-center gap-4"><button onClick={() => changeMonth(-1)} className="p-2 rounded-md hover:bg-gray-700"><ChevronLeft/></button><h2 className="text-xl font-semibold text-white">{currentDate.toLocaleString('default', { month: 'long', year: 'numeric' })}</h2><button onClick={() => changeMonth(1)} className="p-2 rounded-md hover:bg-gray-700"><ChevronRight/></button></div><button onClick={() => setCurrentDate(new Date())} className="px-4 py-2 rounded-md border border-gray-600 hover:bg-gray-700 text-sm">Today</button></header><div className="grid grid-cols-7">{weekDays.map(day => <div key={day} className="text-center p-3 font-semibold text-gray-400 border-b border-r border-gray-700/50">{day}</div>)}{calendarDays}</div></div>);
}
const GanttView = ({ tasks, project, onTasksUpdate, onEditTask, isMasterView, user }) => {
    // A simplified Gantt view is presented here. The full interactive version is complex
    // and requires careful state management. This placeholder ensures stability.
    if (!tasks || tasks.length === 0) {
        return <div className="text-center text-gray-400 py-10">No tasks with dates to display in Gantt chart.</div>;
    }
    return (
        <div className="bg-gray-800/50 p-4 rounded-lg border border-gray-700/50 overflow-y-auto" style={{ maxHeight: '70vh' }}>
            <div className="space-y-2">
                {tasks.map(task => (
                    <div key={task.id} onClick={() => onEditTask(task)} className="p-2 bg-gray-700 rounded-lg hover:bg-indigo-700 cursor-pointer">
                        <p className="font-bold text-white">{task.title}</p>
                        <p className="text-sm text-gray-400">
                            {new Date(task.startDate).toLocaleDateString()} - {new Date(task.dueDate).toLocaleDateString()}
                        </p>
                    </div>
                ))}
            </div>
        </div>
    );
};

// --- Core UI Components ---
const LoadingSpinner = ({ fullScreen }) => (<div className={`flex items-center justify-center ${fullScreen ? "h-screen" : "h-full"} bg-gray-900`}><div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-indigo-500"></div></div>);
const Column = ({ id, title, tasks, onEditTask, onAddTask }) => { const { setNodeRef, isOver } = useSortable({ id: id, data: { type: 'column' } }); return (<SortableContext id={id} items={tasks.map(t => t.id)} strategy={verticalListSortingStrategy}><div ref={setNodeRef} className={`flex flex-col rounded-lg bg-gray-800/50 shadow-md transition-colors ${isOver ? 'bg-gray-700/50' : ''}`}><div className="flex items-center justify-between p-3 border-b border-gray-700/50"><div className="flex items-center space-x-2"><h3 className="font-semibold text-white">{title}</h3><span className="text-sm text-gray-400 bg-gray-700/80 rounded-full px-2 py-0.5">{tasks.length}</span></div><button onClick={onAddTask} className="text-gray-400 hover:text-white"><Plus size={18} /></button></div><div className="flex-grow p-2 space-y-3 min-h-[100px] overflow-y-auto">{tasks.map(task => (<TaskCard key={task.id} task={task} onEditTask={onEditTask} />))}</div></div></SortableContext>);};
const TaskCard = ({ task, onEditTask }) => {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: task.id });
    const style = { transform: CSS.Transform.toString(transform), transition, opacity: isDragging ? 0.5 : 1, boxShadow: isDragging ? '0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1)' : 'none' };
    const priorityStyles = { High: 'bg-red-500/20 text-red-400 border-red-500/30', Medium: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30', Low: 'bg-blue-500/20 text-blue-400 border-blue-500/30', None: 'bg-gray-500/20 text-gray-400 border-gray-500/30' };
    return (<div ref={setNodeRef} style={style} {...attributes} {...listeners} onClick={() => onEditTask(task)} className="bg-gray-800 p-3 rounded-lg border border-gray-700/50 hover:border-indigo-500/50 cursor-pointer transition-all duration-200 ease-in-out transform hover:-translate-y-0.5"><div className="flex justify-between"><p className="text-base font-medium text-gray-200 pr-4">{task.title}</p>{task.isMilestone && <Star size={16} className="text-amber-400" />}</div><div className="flex items-center justify-between mt-3 text-sm"><div className="flex items-center space-x-2"><span className={`px-2 py-0.5 text-xs font-semibold rounded-full border ${priorityStyles[task.priority || 'None'] || ''}`}>{task.priority || 'None'}</span></div><div className="flex items-center space-x-2">{task.deadline && <div className="flex items-center space-x-1 text-red-400" title={`Deadline: ${new Date(task.deadline).toLocaleDateString()}`}><Diamond size={14}/></div>}{task.dueDate && <div className="flex items-center space-x-1 text-gray-400"><CalendarIcon size={14} /><span>{new Date(task.dueDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</span></div>}<div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center" title="Assignee"><User size={14} className="text-gray-300" /></div></div></div></div>);
};
const ListViewRow = ({ task, onEditTask, onDeleteTask }) => {
    const priorityStyles = { High: 'bg-red-500/20 text-red-400', Medium: 'bg-yellow-500/20 text-yellow-400', Low: 'bg-blue-500/20 text-blue-400', None: 'bg-gray-500/20 text-gray-400' };
    return (<div className="flex items-center justify-between p-3 hover:bg-gray-700/50 transition-colors"><div className="flex items-center gap-4 flex-1 cursor-pointer" onClick={() => onEditTask(task)}><span className="text-gray-400 text-sm">{task.id.substring(0, 6)}</span><p className="text-base text-gray-200">{task.title}</p>{task.isMilestone && <Star size={16} className="text-amber-400 ml-2" />}</div><div className="flex items-center gap-6"><span className={`px-3 py-1 text-xs font-semibold rounded-full ${priorityStyles[task.priority || 'None']}`}>{task.priority || 'None'}</span><div className="flex items-center gap-2 text-sm text-gray-400 w-44">{task.dueDate ? <><CalendarIcon size={16} /><span>{new Date(task.dueDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}</span></> : <span>-</span>}</div><div className="flex items-center gap-2 text-sm text-red-400 w-44">{task.deadline ? <><Diamond size={16} /><span>{new Date(task.deadline).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}</span></> : <span>-</span>}</div><div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center" title="Assignee"><User size={16} className="text-gray-300" /></div><button onClick={() => onDeleteTask(task.id)} className="text-gray-400 hover:text-red-400"><Trash2 size={16} /></button></div></div>);
};

// --- Modals ---
const ProjectModal = ({ onClose, onSave }) => { const [name, setName] = useState(''); const handleSubmit = (e) => { e.preventDefault(); if(name.trim()) { onSave(name.trim()); } }; return (<div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center p-4 backdrop-blur-sm" onClick={onClose}><div className="bg-gray-800 rounded-xl shadow-2xl w-full max-w-md border border-gray-700" onClick={e => e.stopPropagation()}><form onSubmit={handleSubmit}><div className="p-6"><h2 className="text-xl font-bold mb-4">New Project</h2><label htmlFor="projectName" className="text-sm font-medium text-gray-300">Project Name</label><input id="projectName" type="text" value={name} onChange={e => setName(e.target.value)} placeholder="e.g. Website Redesign" className="w-full mt-2 bg-gray-700/50 p-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500" autoFocus /></div><div className="flex justify-end items-center p-4 bg-gray-800/50 border-t border-gray-700 rounded-b-xl gap-4"><button type="button" onClick={onClose} className="px-4 py-2 rounded-md text-gray-300 hover:bg-gray-700">Cancel</button><button type="submit" className="px-4 py-2 rounded-md bg-indigo-600 text-white hover:bg-indigo-500">Create Project</button></div></form></div></div>); }
const TaskModal = ({ task, project, user, onClose, onSave, onDelete, onAddComment, statuses }) => {
    const [title, setTitle] = useState(task?.title || '');
    const [description, setDescription] = useState(task?.description || '');
    const [status, setStatus] = useState(task?.status || statuses[0]);
    const [priority, setPriority] = useState(task?.priority || 'None');
    const [startDate, setStartDate] = useState(task?.startDate ? new Date(task.startDate).toISOString().split('T')[0] : '');
    const [dueDate, setDueDate] = useState(task?.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : '');
    const [deadline, setDeadline] = useState(task?.deadline ? new Date(task.deadline).toISOString().split('T')[0] : '');
    const [isMilestone, setIsMilestone] = useState(task?.isMilestone || false);
    const [newComment, setNewComment] = useState("");

    const handleSave = (e) => { e.preventDefault(); if (!title.trim()) return; const taskData = { id: task?.id, projectId: project?.id, title: title.trim(), description: description.trim(), status, priority, startDate: startDate ? new Date(startDate).toISOString() : null, dueDate: dueDate ? new Date(dueDate).toISOString() : null, deadline: deadline ? new Date(deadline).toISOString() : null, isMilestone }; onSave(taskData); };
    const handleAddComment = (e) => { e.preventDefault(); onAddComment(task.id, newComment); setNewComment(""); }

    return (
        <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center p-4 backdrop-blur-sm" onClick={onClose}>
            <div className="bg-gray-800 rounded-xl shadow-2xl w-full max-w-4xl border border-gray-700 grid grid-cols-3" onClick={e => e.stopPropagation()}>
                <div className="col-span-2 border-r border-gray-700">
                    <form onSubmit={handleSave} className="flex flex-col h-full">
                        <div className="p-6">
                            <div className="flex justify-between items-start"><input type="text" value={title} onChange={(e) => setTitle(e.target.value)} placeholder="Task title..." className="w-full bg-transparent text-2xl font-bold text-white focus:outline-none" autoFocus/><button type="button" onClick={() => setIsMilestone(!isMilestone)} title="Set as milestone" className={`p-2 rounded-full ${isMilestone ? 'bg-amber-500/20 text-amber-400' : 'text-gray-500 hover:bg-gray-700'}`}><Star size={20}/></button></div>
                            <textarea value={description} onChange={(e) => setDescription(e.target.value)} placeholder="Add a description..." className="w-full bg-transparent text-gray-300 mt-4 h-20 resize-none focus:outline-none"/>
                        </div>
                        <div className="p-6 border-t border-gray-700 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div><label className="text-xs text-gray-400 font-semibold mb-1 block">Status</label><select value={status} onChange={e => setStatus(e.target.value)} className="w-full bg-gray-700/50 p-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500">{statuses.map(s => <option key={s} value={s}>{s}</option>)}</select></div>
                            <div><label className="text-xs text-gray-400 font-semibold mb-1 block">Priority</label><select value={priority} onChange={e => setPriority(e.target.value)} className="w-full bg-gray-700/50 p-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"><option value="None">None</option><option value="Low">Low</option><option value="Medium">Medium</option><option value="High">High</option></select></div>
                            <div className="lg:col-span-3 grid grid-cols-3 gap-4">
                                <div><label className="text-xs text-gray-400 font-semibold mb-1 block">Start Date</label><input type="date" value={startDate} onChange={e => setStartDate(e.target.value)} className="w-full bg-gray-700/50 p-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500" /></div>
                                <div><label className="text-xs text-gray-400 font-semibold mb-1 block">Due Date</label><input type="date" value={dueDate} onChange={e => setDueDate(e.target.value)} className="w-full bg-gray-700/50 p-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500" /></div>
                                <div><label className="text-xs text-gray-400 font-semibold mb-1 block">Deadline</label><input type="date" value={deadline} onChange={e => setDeadline(e.target.value)} className="w-full bg-gray-700/50 p-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-red-500" /></div>
                            </div>
                        </div>
                        <div className="flex-grow"></div>
                        <div className="flex justify-between items-center p-6 bg-gray-800/50 border-t border-gray-700 rounded-b-xl">{task?.id && <button type="button" onClick={() => onDelete(task.id, project?.id)} className="text-red-400 hover:text-red-300 transition-colors text-sm font-medium flex items-center gap-2"><Trash2 size={16} /> Delete Task</button>}<div className="flex-grow"></div><div className="flex items-center gap-4"><button type="button" onClick={onClose} className="px-4 py-2 rounded-md text-gray-300 hover:bg-gray-700 transition-colors">Cancel</button><button type="submit" className="px-4 py-2 rounded-md bg-indigo-600 text-white hover:bg-indigo-500 transition-colors">{task?.id ? 'Save Changes' : 'Create Task'}</button></div></div>
                    </form>
                </div>
                <div className="col-span-1 flex flex-col">
                    <h3 className="p-4 font-semibold text-white border-b border-gray-700">Activity</h3>
                    <div className="flex-grow p-4 space-y-4 overflow-y-auto">
                        {task?.activity?.sort((a,b) => new Date(b.timestamp) - new Date(a.timestamp)).map(item => <ActivityItem key={item.id} item={item} />)}
                    </div>
                    <div className="p-4 border-t border-gray-700">
                        <form onSubmit={handleAddComment} className="flex gap-2">
                            <input type="text" value={newComment} onChange={e => setNewComment(e.target.value)} placeholder="Add a comment..." className="w-full bg-gray-700/50 p-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500" />
                            <button type="submit" className="px-4 py-2 rounded-md bg-indigo-600 text-white hover:bg-indigo-500 disabled:bg-gray-600" disabled={!newComment.trim()}>Send</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    );
};

const ActivityItem = ({item}) => {
    const formatActivityDate = (timestamp) => {
        if (!timestamp) return 'Just now';
        return new Date(timestamp).toLocaleString();
    }

    if (item.type === 'comment') {
        return (
            <div className="flex gap-3">
                <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center flex-shrink-0"><User size={16} /></div>
                <div>
                    <div className="flex items-center gap-2">
                        <span className="font-semibold text-sm">{item.authorName}</span>
                        <span className="text-xs text-gray-400">{formatActivityDate(item.timestamp)}</span>
                    </div>
                    <p className="text-sm bg-gray-700/50 p-2 rounded-md mt-1">{item.text}</p>
                </div>
            </div>
        )
    }

    if (item.type === 'history' && item.changes) {
        return (
             <div className="flex gap-3">
                <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center flex-shrink-0"><Repeat size={16} /></div>
                <div>
                    {item.changes.map((change, index) => (
                         <div key={index} className="text-sm">
                            <p className="text-gray-300">
                                <span className="font-semibold">{item.changedBy?.substring(0, 12)}...</span> changed <span className="font-semibold">{change.field}</span>
                            </p>
                            <p className="text-gray-400">from "{change.oldValue}" to "{change.newValue}"</p>
                        </div>
                    ))}
                    <span className="text-xs text-gray-500">{formatActivityDate(item.timestamp)}</span>
                </div>
            </div>
        )
    }

    return null;
}
