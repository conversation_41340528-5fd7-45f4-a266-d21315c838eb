<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Momentum: Identity Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Using a modern, reputable library for cryptographic operations -->
    <script src="https://bundle.run/tweetnacl@1.0.3/nacl-fast.js"></script>
    <script src="https://bundle.run/tweetnacl-util@0.15.1"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .putty-canvas {
            background: repeating-linear-gradient(45deg, #2d3748, #2d3748 10px, #4a5568 10px, #4a5568 20px);
        }
    </style>
</head>
<body class="bg-gray-900 text-white flex items-center justify-center min-h-screen">

    <div id="app-container" class="w-full max-w-2xl mx-auto p-8">
        <div class="text-center mb-10">
            <h1 class="text-4xl font-bold text-white">Momentum</h1>
            <p class="text-indigo-400">Decentralized Identity Management</p>
        </div>

        <!-- Main View: Choose or Create -->
        <div id="main-view" class="space-y-6">
            <div id="existing-identities-section" class="bg-gray-800 p-6 rounded-lg hidden">
                <h2 class="text-2xl font-semibold mb-4 text-center">Choose Existing Identity</h2>
                <div id="identities-list" class="space-y-3"></div>
            </div>
            <div class="bg-gray-800 p-6 rounded-lg text-center">
                <h2 class="text-2xl font-semibold mb-4">Create New Identity</h2>
                <p class="text-gray-400 mb-4">Generate a new secure identity to use with Momentum.</p>
                <button id="show-create-view-btn" class="w-full bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                    Create New Identity
                </button>
            </div>
        </div>

        <!-- Login View -->
        <div id="login-view" class="hidden bg-gray-800 p-8 rounded-lg">
            <h2 class="text-2xl font-semibold mb-4">Unlock Identity: <span id="login-identity-name" class="text-indigo-400"></span></h2>
            <form id="login-form">
                <div class="mb-4">
                    <label for="login-password" class="block text-gray-300 text-sm font-bold mb-2">Password</label>
                    <input type="password" id="login-password" class="w-full bg-gray-700 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-500" required>
                </div>
                <div class="flex items-center justify-between mt-6">
                    <button type="button" id="login-back-btn" class="text-gray-400 hover:text-white">Back</button>
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-3 px-4 rounded-lg transition-colors">Unlock &raquo;</button>
                </div>
            </form>
        </div>

        <!-- Create View -->
        <div id="create-view" class="hidden bg-gray-800 p-8 rounded-lg">
            <h2 class="text-2xl font-semibold mb-4">Create New Identity</h2>
            <form id="create-form">
                <div class="mb-4">
                    <label for="create-name" class="block text-gray-300 text-sm font-bold mb-2">Name</label>
                    <input type="text" id="create-name" class="w-full bg-gray-700 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-500" required>
                </div>
                <div class="mb-6">
                    <label for="create-password" class="block text-gray-300 text-sm font-bold mb-2">Password</label>
                    <input type="password" id="create-password" class="w-full bg-gray-700 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-500" required>
                </div>
                
                <div class="mb-4 p-4 border border-gray-700 rounded-lg">
                    <p class="text-sm text-gray-300 mb-2">Move your mouse over the area below to generate randomness for your security keys.</p>
                    <canvas id="putty-canvas" class="w-full h-40 rounded-md putty-canvas cursor-crosshair"></canvas>
                    <div class="w-full bg-gray-700 rounded-full h-2.5 mt-2">
                        <div id="entropy-progress" class="bg-green-500 h-2.5 rounded-full" style="width: 0%"></div>
                    </div>
                </div>

                <div class="flex items-center justify-between mt-6">
                    <button type="button" id="create-back-btn" class="text-gray-400 hover:text-white">Back</button>
                    <button type="submit" id="generate-keys-btn" class="bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-3 px-4 rounded-lg transition-colors disabled:bg-gray-600 disabled:cursor-not-allowed" disabled>
                        Generate & Save Identity
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        const appContainer = document.getElementById('app-container');
        const mainView = document.getElementById('main-view');
        const loginView = document.getElementById('login-view');
        const createView = document.getElementById('create-view');

        const showCreateViewBtn = document.getElementById('show-create-view-btn');
        const loginBackBtn = document.getElementById('login-back-btn');
        const createBackBtn = document.getElementById('create-back-btn');
        
        const existingIdentitiesSection = document.getElementById('existing-identities-section');
        const identitiesList = document.getElementById('identities-list');
        const loginIdentityName = document.getElementById('login-identity-name');

        const createForm = document.getElementById('create-form');
        const loginForm = document.getElementById('login-form');
        const generateKeysBtn = document.getElementById('generate-keys-btn');
        
        const canvas = document.getElementById('putty-canvas');
        const ctx = canvas.getContext('2d');
        const entropyProgress = document.getElementById('entropy-progress');
        
        let entropy = new Uint8Array(32); // nacl.box.secretKeyLength
        let entropyCollected = 0;
        const requiredEntropy = 1024;
        let selectedIdentity = null;

        // --- View Management ---
        const showView = (view) => {
            mainView.classList.add('hidden');
            loginView.classList.add('hidden');
            createView.classList.add('hidden');
            view.classList.remove('hidden');
        };

        showCreateViewBtn.addEventListener('click', () => showView(createView));
        loginBackBtn.addEventListener('click', () => {
            selectedIdentity = null;
            showView(mainView);
            loadIdentities();
        });
        createBackBtn.addEventListener('click', () => showView(mainView));

        // --- Cryptography Helpers ---
        // These use the Web Crypto API for robust, standardized encryption
        async function encryptData(data, password) {
            const iv = crypto.getRandomValues(new Uint8Array(12));
            const salt = crypto.getRandomValues(new Uint8Array(16));
            const keyMaterial = await crypto.subtle.importKey('raw', new TextEncoder().encode(password), { name: 'PBKDF2' }, false, ['deriveKey']);
            const key = await crypto.subtle.deriveKey({ name: 'PBKDF2', salt, iterations: 100000, hash: 'SHA-256' }, keyMaterial, { name: 'AES-GCM', length: 256 }, true, ['encrypt', 'decrypt']);
            const encrypted = await crypto.subtle.encrypt({ name: 'AES-GCM', iv }, key, new TextEncoder().encode(data));
            return {
                encrypted: nacl.util.encodeBase64(encrypted),
                iv: nacl.util.encodeBase64(iv),
                salt: nacl.util.encodeBase64(salt)
            };
        }

        async function decryptData(encryptedObj, password) {
            try {
                const iv = nacl.util.decodeBase64(encryptedObj.iv);
                const salt = nacl.util.decodeBase64(encryptedObj.salt);
                const encryptedData = nacl.util.decodeBase64(encryptedObj.encrypted);
                const keyMaterial = await crypto.subtle.importKey('raw', new TextEncoder().encode(password), { name: 'PBKDF2' }, false, ['deriveKey']);
                const key = await crypto.subtle.deriveKey({ name: 'PBKDF2', salt, iterations: 100000, hash: 'SHA-256' }, keyMaterial, { name: 'AES-GCM', length: 256 }, true, ['encrypt', 'decrypt']);
                const decrypted = await crypto.subtle.decrypt({ name: 'AES-GCM', iv }, key, encryptedData);
                return new TextDecoder().decode(decrypted);
            } catch (e) {
                console.error("Decryption failed:", e);
                return null;
            }
        }
        
        // --- Identity Management ---
        const loadIdentities = () => {
            const identities = JSON.parse(localStorage.getItem('momentum_identities')) || [];
            identitiesList.innerHTML = '';
            if (identities.length > 0) {
                existingIdentitiesSection.classList.remove('hidden');
                identities.forEach(id => {
                    const button = document.createElement('button');
                    button.className = "w-full text-left bg-gray-700 hover:bg-gray-600 text-white font-bold py-3 px-4 rounded-lg transition-colors";
                    button.innerHTML = `<span>${id.name}</span><br><span class="text-xs text-gray-400 font-mono">${id.publicKey.substring(0, 24)}...</span>`;
                    button.onclick = () => {
                        selectedIdentity = id;
                        loginIdentityName.textContent = id.name;
                        showView(loginView);
                    };
                    identitiesList.appendChild(button);
                });
            } else {
                existingIdentitiesSection.classList.add('hidden');
            }
        };
        
        // --- Key Generation & Entropy ---
        const updateEntropy = (x, y) => {
            if (entropyCollected >= requiredEntropy) return;
            
            const time = performance.now();
            entropy[entropyCollected % 32] ^= Math.floor(x * time) & 0xff;
            entropy[(entropyCollected + 1) % 32] ^= Math.floor(y * time) & 0xff;
            entropyCollected++;
            
            const progress = Math.min(100, (entropyCollected / requiredEntropy) * 100);
            entropyProgress.style.width = `${progress}%`;
            
            if (progress >= 100) {
                generateKeysBtn.disabled = false;
            }
            
            // Visualization
            ctx.fillStyle = `rgba(${entropy[0]}, ${entropy[10]}, ${entropy[20]}, 0.1)`;
            ctx.beginPath();
            ctx.arc(x % canvas.width, y % canvas.height, entropyCollected % 20, 0, Math.PI * 2);
            ctx.fill();
        };
        
        canvas.addEventListener('mousemove', e => {
            const rect = canvas.getBoundingClientRect();
            updateEntropy(e.clientX - rect.left, e.clientY - rect.top);
        });
        
        createForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            generateKeysBtn.disabled = true;
            generateKeysBtn.textContent = 'Generating...';

            const name = document.getElementById('create-name').value;
            const password = document.getElementById('create-password').value;

            // Use the collected entropy to seed the keypair generation
            const keyPair = nacl.box.keyPair.fromSecretKey(entropy);
            
            const publicKey = nacl.util.encodeBase64(keyPair.publicKey);
            const secretKey = nacl.util.encodeBase64(keyPair.secretKey);
            
            const encryptedSecretKey = await encryptData(secretKey, password);
            
            const newIdentity = {
                name,
                publicKey,
                encryptedSecretKey
            };
            
            const identities = JSON.parse(localStorage.getItem('momentum_identities')) || [];
            identities.push(newIdentity);
            localStorage.setItem('momentum_identities', JSON.stringify(identities));
            
            alert(`Identity for "${name}" created! Please log in.`);
            
            // Prompt to download private key backup
            const backupData = { name, publicKey, encryptedSecretKey };
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(backupData, null, 2));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", `${name}_momentum_identity.json`);
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();
            
            showView(mainView);
            loadIdentities();
            generateKeysBtn.textContent = 'Generate & Save Identity';
        });

        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const password = document.getElementById('login-password').value;
            if (!selectedIdentity || !password) return;

            const decryptedSecretKey = await decryptData(selectedIdentity.encryptedSecretKey, password);

            if (decryptedSecretKey) {
                // SUCCESS!
                const sessionIdentity = {
                    name: selectedIdentity.name,
                    publicKey: selectedIdentity.publicKey,
                    secretKey: decryptedSecretKey
                };
                sessionStorage.setItem('momentum_session', JSON.stringify(sessionIdentity));
                // Redirect to the main app
                window.location.href = './index.html'; // Assume main app is in index.html
            } else {
                alert('Decryption failed. Incorrect password.');
            }
        });

        // --- Initial Load ---
        window.onload = () => {
            // Redirect if already logged in
            if (sessionStorage.getItem('momentum_session')) {
                window.location.href = './index.html';
            }
            loadIdentities();
            showView(mainView);
        };
    </script>
</body>
</html>