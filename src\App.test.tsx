import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import App from './App';

// Mock sessionStorage for testing
const mockSessionStorage = {
  getItem: (key: string) => {
    if (key === 'momentum_session') {
      return JSON.stringify({
        name: 'Test User',
        publicKey: 'test-public-key',
        secretKey: 'test-secret-key'
      });
    }
    return null;
  },
  setItem: () => {},
  removeItem: () => {},
  clear: () => {},
  length: 0,
  key: () => null
};

Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage
});

// Mock localStorage for testing
const mockLocalStorage = {
  getItem: () => '[]',
  setItem: () => {},
  removeItem: () => {},
  clear: () => {},
  length: 0,
  key: () => null
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

describe('App', () => {
  it('renders the app with authenticated user', () => {
    render(<App />);
    
    // Check if the main app elements are present
    expect(screen.getByText('Momentum')).toBeInTheDocument();
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
  });
});
